<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Company Deletion Test - Super Admin Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="super-admin-dashboard.css">
    <style>
        body {
            padding: 2rem;
            background: #f9fafb;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
        }
        .test-button {
            background-color: #3b82f6;
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 0.375rem;
            cursor: pointer;
            margin: 0.5rem;
        }
        .test-button:hover {
            background-color: #2563eb;
        }
        .delete-test-button {
            background-color: #dc2626;
        }
        .delete-test-button:hover {
            background-color: #b91c1c;
        }
        .test-output {
            background-color: #f3f4f6;
            padding: 1rem;
            border-radius: 0.375rem;
            margin-top: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
        }
    </style>
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>
</head>
<body>
    <div class="test-container">
        <h1>Company Deletion Feature Test</h1>
        <p>This page tests the company deletion functionality for the Super Admin Dashboard.</p>
        
        <div class="test-section">
            <h2>1. Modal Test</h2>
            <p>Test the warning modal display and styling:</p>
            <button class="test-button" onclick="testWarningModal()">Test Warning Modal</button>
            <div id="modal-test-output" class="test-output" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>2. Company Data Retrieval Test</h2>
            <p>Test fetching company data with associated admins:</p>
            <input type="text" id="test-company-id" placeholder="Enter company ID" style="margin-right: 0.5rem; padding: 0.5rem;">
            <button class="test-button" onclick="testCompanyDataRetrieval()">Test Data Retrieval</button>
            <div id="data-test-output" class="test-output" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>3. Delete Button Styling & Loading State Test</h2>
            <p>Test the delete button appearance and loading states:</p>
            <div class="action-buttons">
                <button class="action-btn view-btn">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </button>
                <button class="action-btn delete-btn" id="test-delete-btn">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            </div>
            <button class="test-button" onclick="testDeleteButtonLoadingState()">Test Loading State</button>
        </div>

        <div class="test-section">
            <h2>4. Notification Test</h2>
            <p>Test notification display:</p>
            <button class="test-button" onclick="testNotification('info')">Info Notification</button>
            <button class="test-button" onclick="testNotification('success')">Success Notification</button>
            <button class="test-button" onclick="testNotification('error')">Error Notification</button>
            <button class="test-button" onclick="testNotification('warning')">Warning Notification</button>
        </div>

        <div class="test-section">
            <h2>5. Server Endpoint Test</h2>
            <p>Test the server deletion endpoint (with mock data):</p>
            <button class="test-button delete-test-button" onclick="testServerEndpoint()">Test Server Endpoint</button>
            <div id="server-test-output" class="test-output" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>6. UI Animation Test</h2>
            <p>Test the immediate row removal animation:</p>
            <div class="table-wrapper">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Company Name</th>
                            <th>Admin Email</th>
                            <th>User Count</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="company-row" data-company-id="test-company-1">
                            <td>
                                <div class="company-info">
                                    <div class="company-name-display">Test Company 1</div>
                                    <div class="company-id">test-company-1</div>
                                </div>
                            </td>
                            <td><EMAIL></td>
                            <td>5 users</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-btn delete-btn" onclick="testRowRemoval('test-company-1')">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="company-row" data-company-id="test-company-2">
                            <td>
                                <div class="company-info">
                                    <div class="company-name-display">Test Company 2</div>
                                    <div class="company-id">test-company-2</div>
                                </div>
                            </td>
                            <td><EMAIL></td>
                            <td>3 users</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-btn delete-btn" onclick="testRowRemoval('test-company-2')">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="table-footer">
                    <div class="table-info">Showing 2 companies</div>
                </div>
            </div>
            <button class="test-button" onclick="resetTestTable()">Reset Test Table</button>
        </div>

        <div class="test-section">
            <h2>7. Full Integration Test</h2>
            <p><strong>⚠️ Warning:</strong> This will test the complete deletion workflow. Only use with test data!</p>
            <input type="text" id="integration-company-id" placeholder="Test company ID" style="margin-right: 0.5rem; padding: 0.5rem;">
            <input type="text" id="integration-company-name" placeholder="Test company name" style="margin-right: 0.5rem; padding: 0.5rem;">
            <button class="test-button delete-test-button" onclick="testFullIntegration()">⚠️ Test Full Deletion</button>
            <div id="integration-test-output" class="test-output" style="display: none;"></div>
        </div>
    </div>

    <!-- Include required scripts -->
    <script src="warning-modal.js"></script>
    <script>
        // Initialize Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
            authDomain: "barefoot-elearning-app.firebaseapp.com",
            projectId: "barefoot-elearning-app",
            databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
            storageBucket: "barefoot-elearning-app.appspot.com",
            messagingSenderId: "170819735788",
            appId: "1:170819735788:web:223af318437eb5d947d5c9"
        };

        if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
        }
        const db = firebase.firestore();

        // Test functions
        function testWarningModal() {
            const output = document.getElementById('modal-test-output');
            output.style.display = 'block';
            output.innerHTML = 'Testing warning modal...';

            if (window.WarningModal) {
                window.WarningModal.show({
                    title: 'Test Company Deletion',
                    message: `
                        <div class="company-delete-warning">
                            <p><strong>This is a test modal for company deletion:</strong></p>
                            <ul>
                                <li>Company: <strong>Test Company Ltd</strong></li>
                                <li>All 2 associated admin account(s)</li>
                                <li>All 5 user record(s) in this company</li>
                                <li>All authentication records for admin accounts</li>
                            </ul>
                            <div class="admin-list-warning">
                                <h4>Associated Admin Accounts to be deleted:</h4>
                                <ul>
                                    <li><strong>John Doe</strong> (<EMAIL>)</li>
                                    <li><strong>Jane Smith</strong> (<EMAIL>)</li>
                                </ul>
                            </div>
                            <p class="warning-text"><strong>⚠️ This action cannot be undone!</strong></p>
                        </div>
                    `,
                    confirmText: 'Delete Company',
                    cancelText: 'Cancel',
                    icon: 'warning',
                    confirmButtonStyle: 'danger'
                }).then(result => {
                    output.innerHTML = `Modal test completed. User ${result ? 'confirmed' : 'cancelled'} the action.`;
                });
            } else {
                output.innerHTML = 'Error: WarningModal not available';
            }
        }

        async function testCompanyDataRetrieval() {
            const companyId = document.getElementById('test-company-id').value;
            const output = document.getElementById('data-test-output');
            output.style.display = 'block';
            
            if (!companyId) {
                output.innerHTML = 'Please enter a company ID';
                return;
            }

            output.innerHTML = 'Retrieving company data...';

            try {
                const companyData = await getCompanyWithAdmins(companyId);
                output.innerHTML = `Company data retrieved successfully:\n${JSON.stringify(companyData, null, 2)}`;
            } catch (error) {
                output.innerHTML = `Error retrieving company data: ${error.message}`;
            }
        }

        function testNotification(type) {
            const messages = {
                info: 'This is an info notification',
                success: 'Company deleted successfully!',
                error: 'Error deleting company',
                warning: 'This is a warning notification'
            };
            showNotification(messages[type], type);
        }

        function testDeleteButtonLoadingState() {
            const deleteBtn = document.getElementById('test-delete-btn');
            if (!deleteBtn) return;

            // Set loading state
            deleteBtn.disabled = true;
            deleteBtn.classList.add('loading');
            deleteBtn.innerHTML = `
                <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
            `;

            // Restore normal state after 3 seconds
            setTimeout(() => {
                deleteBtn.disabled = false;
                deleteBtn.classList.remove('loading');
                deleteBtn.innerHTML = `
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                `;
                showNotification('Delete button loading state test completed', 'success');
            }, 3000);

            showNotification('Testing delete button loading state for 3 seconds...', 'info');
        }

        function testRowRemoval(companyId) {
            // Test the row removal animation
            removeCompanyRowFromDOM(companyId).then(() => {
                showNotification(`Test row ${companyId} removed successfully`, 'success');
            });
        }

        function resetTestTable() {
            // Reset the test table to original state
            const tableBody = document.querySelector('.test-section:nth-of-type(6) tbody');
            if (!tableBody) return;

            tableBody.innerHTML = `
                <tr class="company-row" data-company-id="test-company-1">
                    <td>
                        <div class="company-info">
                            <div class="company-name-display">Test Company 1</div>
                            <div class="company-id">test-company-1</div>
                        </div>
                    </td>
                    <td><EMAIL></td>
                    <td>5 users</td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn delete-btn" onclick="testRowRemoval('test-company-1')">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </td>
                </tr>
                <tr class="company-row" data-company-id="test-company-2">
                    <td>
                        <div class="company-info">
                            <div class="company-name-display">Test Company 2</div>
                            <div class="company-id">test-company-2</div>
                        </div>
                    </td>
                    <td><EMAIL></td>
                    <td>3 users</td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn delete-btn" onclick="testRowRemoval('test-company-2')">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </td>
                </tr>
            `;

            // Reset footer count
            const tableFooter = document.querySelector('.test-section:nth-of-type(6) .table-info');
            if (tableFooter) {
                tableFooter.textContent = 'Showing 2 companies';
            }

            showNotification('Test table reset successfully', 'info');
        }

        // Include the row removal functions from the main dashboard
        function removeCompanyRowFromDOM(companyId) {
            return new Promise((resolve) => {
                const companyRow = document.querySelector(`tr.company-row[data-company-id="${companyId}"]`);
                if (!companyRow) {
                    console.warn(`Company row with ID ${companyId} not found in DOM`);
                    resolve();
                    return;
                }

                // Add CSS animation class for smooth removal
                companyRow.classList.add('company-row-fade-out');

                // Listen for animation end event for better performance
                const handleAnimationEnd = () => {
                    companyRow.removeEventListener('animationend', handleAnimationEnd);
                    if (companyRow.parentNode) {
                        companyRow.remove();
                        updateCompanyTableFooter();
                    }
                    resolve();
                };

                companyRow.addEventListener('animationend', handleAnimationEnd);

                // Fallback timeout in case animation event doesn't fire
                setTimeout(() => {
                    if (companyRow.parentNode) {
                        companyRow.removeEventListener('animationend', handleAnimationEnd);
                        companyRow.remove();
                        updateCompanyTableFooter();
                        resolve();
                    }
                }, 350);
            });
        }

        function updateCompanyTableFooter() {
            const tableFooter = document.querySelector('.test-section:nth-of-type(6) .table-footer .table-info');
            if (!tableFooter) return;

            // Count remaining visible company rows in the test table
            const visibleRows = document.querySelectorAll('.test-section:nth-of-type(6) .company-row').length;
            tableFooter.textContent = `Showing ${visibleRows} companies`;

            // Add a subtle animation to indicate the count changed
            tableFooter.style.transition = 'all 0.2s ease-out';
            tableFooter.style.transform = 'scale(1.05)';
            tableFooter.style.color = '#059669'; // Green color to indicate success

            setTimeout(() => {
                tableFooter.style.transform = 'scale(1)';
                tableFooter.style.color = ''; // Reset to default color
            }, 200);
        }

        async function testServerEndpoint() {
            const output = document.getElementById('server-test-output');
            output.style.display = 'block';
            output.innerHTML = 'Testing server endpoint with mock data...';

            try {
                const response = await fetch('/delete-company', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        companyId: 'test-company-id',
                        companyName: 'Test Company',
                        adminEmails: ['<EMAIL>']
                    })
                });

                const result = await response.json();
                output.innerHTML = `Server response:\nStatus: ${response.status}\nResponse: ${JSON.stringify(result, null, 2)}`;
            } catch (error) {
                output.innerHTML = `Server test error: ${error.message}`;
            }
        }

        async function testFullIntegration() {
            const companyId = document.getElementById('integration-company-id').value;
            const companyName = document.getElementById('integration-company-name').value;
            const output = document.getElementById('integration-test-output');
            output.style.display = 'block';

            if (!companyId || !companyName) {
                output.innerHTML = 'Please enter both company ID and name';
                return;
            }

            output.innerHTML = 'Starting full integration test...';

            try {
                // This would call the actual deletion function
                output.innerHTML = 'Full integration test would be called here.\nFor safety, actual deletion is not performed in test mode.';
                showNotification('Integration test completed (no actual deletion performed)', 'info');
            } catch (error) {
                output.innerHTML = `Integration test error: ${error.message}`;
            }
        }

        // Include the necessary functions from the main dashboard
        // (Simplified versions for testing)
        async function getCompanyWithAdmins(companyId) {
            try {
                const companyDoc = await db.collection('companies').doc(companyId).get();
                if (!companyDoc.exists) {
                    throw new Error('Company not found');
                }

                const companyData = companyDoc.data();
                const usersSnapshot = await companyDoc.ref.collection('users').get();
                const userCount = usersSnapshot.size;

                const adminsSnapshot = await db.collection('Admins')
                    .where('company', '==', companyData.name)
                    .get();

                const admins = [];
                adminsSnapshot.forEach(doc => {
                    const adminData = doc.data();
                    admins.push({
                        id: doc.id,
                        email: adminData.email,
                        firstname: adminData.firstname || 'Unknown',
                        lastname: adminData.lastname || 'User'
                    });
                });

                return {
                    id: companyId,
                    name: companyData.name,
                    adminEmail: companyData.adminEmail,
                    userCount: userCount,
                    admins: admins
                };
            } catch (error) {
                console.error('Error getting company with admins:', error);
                throw error;
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <span class="notification-message">${message}</span>
                    <button class="notification-close">&times;</button>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);

            const closeBtn = notification.querySelector('.notification-close');
            closeBtn.addEventListener('click', () => {
                notification.remove();
            });

            setTimeout(() => {
                notification.classList.add('show');
            }, 10);
        }
    </script>
</body>
</html>
